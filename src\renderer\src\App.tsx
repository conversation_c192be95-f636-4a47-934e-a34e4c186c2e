import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { Loader2, Code2, Spa<PERSON><PERSON> } from 'lucide-react'

// Type declaration for electron API
declare global {
  interface Window {
    electronAPI: {
      getVersion: () => Promise<string>
      getPlatform: () => Promise<string>
    }
  }
}

function App() {
  const [isLoading, setIsLoading] = useState(true)
  const [appInfo, setAppInfo] = useState<{
    version: string
    platform: string
  } | null>(null)

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Simulate loading time for smooth UX
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Get app information
        const version = await window.electronAPI.getVersion()
        const platform = await window.electronAPI.getPlatform()
        
        setAppInfo({ version, platform })
      } catch (error) {
        console.error('Failed to initialize app:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeApp()
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center w-full h-screen bg-background">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center space-y-4"
        >
          <div className="relative">
            <Code2 className="w-12 h-12 text-primary" />
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="absolute -top-1 -right-1"
            >
              <Sparkles className="w-6 h-6 text-accent" />
            </motion.div>
          </div>
          <div className="flex items-center space-x-2">
            <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              AI Code Editor yükleniyor...
            </span>
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col w-full h-screen bg-background"
    >
      {/* Title Bar */}
      <div className="flex items-center justify-between h-8 bg-card border-b border-border px-4">
        <div className="flex items-center space-x-2">
          <Code2 className="w-4 h-4 text-primary" />
          <span className="text-sm font-medium">AI Code Editor</span>
        </div>
        <div className="text-xs text-muted-foreground">
          v{appInfo?.version} • {appInfo?.platform}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <motion.div
          initial={{ x: -300 }}
          animate={{ x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="w-64 sidebar flex flex-col"
        >
          <div className="panel-header">
            <h2 className="text-sm font-semibold">Explorer</h2>
          </div>
          <div className="panel-content flex-1">
            <div className="text-sm text-muted-foreground">
              Proje açmak için File → Open Project menüsünü kullanın
            </div>
          </div>
        </motion.div>

        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Tab Bar */}
          <motion.div
            initial={{ y: -50 }}
            animate={{ y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="h-10 bg-card border-b border-border flex items-center px-4"
          >
            <span className="text-sm text-muted-foreground">
              Hoş geldiniz! Bir dosya açın veya yeni proje oluşturun.
            </span>
          </motion.div>

          {/* Editor Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="flex-1 editor-container flex items-center justify-center"
          >
            <div className="text-center space-y-4">
              <div className="relative mx-auto w-24 h-24">
                <Code2 className="w-24 h-24 text-muted-foreground/50" />
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity, 
                    ease: "easeInOut" 
                  }}
                  className="absolute top-0 left-0 w-24 h-24"
                >
                  <Sparkles className="w-24 h-24 text-primary/30" />
                </motion.div>
              </div>
              <div className="space-y-2">
                <h1 className="text-2xl font-bold text-foreground">
                  AI Code Editor
                </h1>
                <p className="text-muted-foreground max-w-md">
                  Gemini 2.5 Flash Pro ile güçlendirilmiş modern kod editörü. 
                  Akıllı kod tamamlama, gerçek zamanlı analiz ve AI asistanı ile 
                  geliştirme deneyiminizi yükseltin.
                </p>
              </div>
              <div className="flex flex-col space-y-2 text-sm text-muted-foreground">
                <div>🚀 Proje açmak için Ctrl+O</div>
                <div>💬 AI Chat için Ctrl+Shift+C</div>
                <div>⚙️ Ayarlar için Ctrl+,</div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Right Panel (Chat) */}
        <motion.div
          initial={{ x: 300 }}
          animate={{ x: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="w-80 panel flex flex-col"
        >
          <div className="panel-header">
            <h2 className="text-sm font-semibold flex items-center space-x-2">
              <Sparkles className="w-4 h-4" />
              <span>AI Assistant</span>
            </h2>
          </div>
          <div className="panel-content flex-1">
            <div className="text-sm text-muted-foreground">
              AI asistanınız hazır! Kod yazma, debugging ve optimizasyon 
              konularında yardım almak için mesaj yazın.
            </div>
          </div>
        </motion.div>
      </div>

      {/* Status Bar */}
      <motion.div
        initial={{ y: 50 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        className="h-6 bg-card border-t border-border flex items-center justify-between px-4 text-xs text-muted-foreground"
      >
        <div className="flex items-center space-x-4">
          <span>Ready</span>
          <span>•</span>
          <span>AI Model: Gemini 2.5 Flash Pro</span>
        </div>
        <div className="flex items-center space-x-4">
          <span>Tokens: 0/4096</span>
          <span>•</span>
          <span>UTF-8</span>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default App
