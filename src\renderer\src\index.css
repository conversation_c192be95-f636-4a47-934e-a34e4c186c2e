@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted/20;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-md;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary/20;
  }
  
  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-default {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-outline {
    @apply border border-input hover:bg-accent hover:text-accent-foreground;
  }
  
  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Card styles */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  /* Panel styles */
  .panel {
    @apply bg-card border-r border-border;
  }
  
  .panel-header {
    @apply flex items-center justify-between p-4 border-b border-border;
  }
  
  .panel-content {
    @apply p-4;
  }
  
  /* Editor styles */
  .editor-container {
    @apply bg-editor text-editor-foreground;
  }
  
  /* Terminal styles */
  .terminal-container {
    @apply bg-terminal text-terminal-foreground font-mono;
  }
  
  /* Sidebar styles */
  .sidebar {
    @apply bg-sidebar text-sidebar-foreground border-r border-border;
  }
  
  /* Animation utilities */
  .animate-in {
    animation: animate-in 0.2s ease-out;
  }
  
  .animate-out {
    animation: animate-out 0.2s ease-out;
  }
  
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes animate-out {
    from {
      opacity: 1;
      transform: scale(1);
    }
    to {
      opacity: 0;
      transform: scale(0.95);
    }
  }
  
  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }
  
  /* Code highlighting */
  .code-highlight {
    @apply bg-accent/50 rounded px-1;
  }
  
  /* Drag and drop */
  .drag-over {
    @apply bg-accent/20 border-accent border-dashed;
  }
  
  /* Resizable panels */
  .resize-handle {
    @apply bg-border hover:bg-accent transition-colors cursor-col-resize;
  }
  
  .resize-handle:hover {
    @apply bg-accent;
  }
  
  /* Context menu */
  .context-menu {
    @apply bg-popover text-popover-foreground border border-border rounded-md shadow-lg;
  }
  
  /* Tooltip */
  .tooltip {
    @apply bg-popover text-popover-foreground border border-border rounded px-2 py-1 text-xs;
  }
}
