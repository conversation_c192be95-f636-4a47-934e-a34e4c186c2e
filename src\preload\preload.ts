import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  getPlatform: () => ipcRenderer.invoke('app:getPlatform'),

  // File system operations (will be implemented later)
  openProject: (path: string) => ipcRenderer.invoke('fs:openProject', path),
  readFile: (path: string) => ipcRenderer.invoke('fs:readFile', path),
  writeFile: (path: string, content: string) => ipcRenderer.invoke('fs:writeFile', path, content),
  createFile: (path: string, content: string) => ipcRenderer.invoke('fs:createFile', path, content),
  createFolder: (path: string) => ipcRenderer.invoke('fs:createFolder', path),
  deleteFile: (path: string) => ipcRenderer.invoke('fs:deleteFile', path),
  watchFiles: (callback: (event: any) => void) => {
    ipcRenderer.on('fs:fileChanged', callback)
    return () => ipcRenderer.removeListener('fs:fileChanged', callback)
  },

  // AI service operations (will be implemented later)
  sendAIMessage: (message: string, context?: any) => ipcRenderer.invoke('ai:sendMessage', message, context),
  setAIModel: (model: string) => ipcRenderer.invoke('ai:setModel', model),
  getAIModels: () => ipcRenderer.invoke('ai:getModels'),
  validateAPIKey: (provider: string, key: string) => ipcRenderer.invoke('ai:validateKey', provider, key),

  // Terminal operations (will be implemented later)
  createTerminal: (id: string) => ipcRenderer.invoke('terminal:create', id),
  executeCommand: (terminalId: string, command: string) => ipcRenderer.invoke('terminal:execute', terminalId, command),
  killTerminal: (terminalId: string) => ipcRenderer.invoke('terminal:kill', terminalId),
  onTerminalOutput: (callback: (data: any) => void) => {
    ipcRenderer.on('terminal:output', callback)
    return () => ipcRenderer.removeListener('terminal:output', callback)
  },

  // Settings operations (will be implemented later)
  getSetting: (key: string) => ipcRenderer.invoke('settings:get', key),
  setSetting: (key: string, value: any) => ipcRenderer.invoke('settings:set', key, value),
  getAllSettings: () => ipcRenderer.invoke('settings:getAll'),

  // Window operations
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
  isMaximized: () => ipcRenderer.invoke('window:isMaximized'),

  // Dialog operations
  showOpenDialog: (options: any) => ipcRenderer.invoke('dialog:showOpen', options),
  showSaveDialog: (options: any) => ipcRenderer.invoke('dialog:showSave', options),
  showMessageBox: (options: any) => ipcRenderer.invoke('dialog:showMessage', options),

  // External integrations (will be implemented later)
  searchWeb: (query: string) => ipcRenderer.invoke('web:search', query),
  scrapeWebsite: (url: string) => ipcRenderer.invoke('web:scrape', url),
  createFigmaDesign: (prompt: string) => ipcRenderer.invoke('figma:create', prompt),
  analyzeFigmaDesign: (url: string) => ipcRenderer.invoke('figma:analyze', url),
}

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// Type definitions for the exposed API
export type ElectronAPI = typeof electronAPI
